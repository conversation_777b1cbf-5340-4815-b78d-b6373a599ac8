import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { useRFQStore } from "@/store/rfqStore";
import type { Vendor } from "@/types/p2p";
import { useState } from "react";

export function VendorEditorDialog({ vendor }: { vendor?: Vendor }) {
  const setState = useRFQStore.setState;
  const saveVendor = useRFQStore(s => s.saveVendor);

  const [open, setOpen] = useState(false);
  const [form, setForm] = useState<Partial<Vendor>>(vendor || {});
  const [syncing, setSyncing] = useState(false);

  function onChange<K extends keyof Vendor>(k: K, v: Vendor[K]) {
    setForm(prev => ({ ...prev, [k]: v }));
  }

  async function onSave() {
    const v: Vendor = {
      id: form.id || `temp_${Math.random().toString(36).slice(2,8)}`,
      name: form.name || "",
      email: form.email,
      phone: form.phone,
      company: form.company,
      address: form.address,
      website: form.website,
      favorite: !!form.favorite,
      notes: form.notes,
      etag: form.etag,
    };

    // optimistic
    setState((s) => ({ vendors: upsert(s.vendors, v) }));
    setSyncing(true);
    await saveVendor?.(v);
    setSyncing(false);
    setOpen(false);
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">{vendor ? "Edit Vendor" : "Manage Vendors"}</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{vendor ? "Edit Vendor" : "New Vendor"}</DialogTitle>
        </DialogHeader>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div>
            <Label className="text-xs">Name</Label>
            <Input value={form.name || ""} onChange={(e)=>onChange('name', e.target.value)} />
          </div>
          <div>
            <Label className="text-xs">Email</Label>
            <Input type="email" value={form.email || ""} onChange={(e)=>onChange('email', e.target.value)} />
          </div>
          <div>
            <Label className="text-xs">Phone</Label>
            <Input value={form.phone || ""} onChange={(e)=>onChange('phone', e.target.value)} />
          </div>
          <div>
            <Label className="text-xs">Website</Label>
            <Input value={form.website || ""} onChange={(e)=>onChange('website', e.target.value)} />
          </div>
          <div className="md:col-span-2">
            <Label className="text-xs">Address</Label>
            <Input value={form.address || ""} onChange={(e)=>onChange('address', e.target.value)} />
          </div>
          <div className="flex items-center gap-2">
            <Checkbox checked={!!form.favorite} onCheckedChange={(v)=>onChange('favorite', !!v)} id="favorite" />
            <Label htmlFor="favorite">Favorite</Label>
          </div>
          <div className="md:col-span-2">
            <Label className="text-xs">Notes</Label>
            <Input value={form.notes || ""} onChange={(e)=>onChange('notes', e.target.value)} />
          </div>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-xs text-muted-foreground">{syncing ? 'Syncing…' : ' '}</span>
          <div className="flex justify-end gap-2">
            <Button variant="ghost" onClick={()=>setOpen(false)}>Cancel</Button>
            <Button onClick={onSave}>Save</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

function upsert(list: Vendor[], v: Vendor): Vendor[] {
  const i = list.findIndex(x => x.id === v.id);
  if (i >= 0) {
    const copy = [...list];
    copy[i] = { ...copy[i], ...v };
    return copy;
  }
  return [v, ...list];
}

