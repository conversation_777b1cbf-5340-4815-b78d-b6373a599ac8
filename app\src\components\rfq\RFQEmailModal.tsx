import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Mail, 
  Plus, 
  X, 
  Send, 
  Eye, 
  EyeOff, 
  AlertCircle, 
  CheckCircle,
  Users
} from "lucide-react";
import { useRFQStore } from "@/store/rfqStore";
import type { RFQ, Vendor } from "@/store/rfqStore";

interface RFQEmailModalProps {
  isOpen: boolean;
  onClose: () => void;
  rfq: RFQ | null;
  onSendEmail: (emailData: EmailData) => Promise<void>;
}

export interface EmailData {
  rfqId: string;
  recipients: string[];
  subject: string;
  message: string;
  includeAttachments: boolean;
}

export function RFQEmailModal({ isOpen, onClose, rfq, onSendEmail }: RFQEmailModalProps) {
  const vendors = useRFQStore(s => s.vendors);
  const [recipients, setRecipients] = useState<string[]>([]);
  const [newRecipient, setNewRecipient] = useState('');
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [includeAttachments, setIncludeAttachments] = useState(true);
  const [showPreview, setShowPreview] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Reset form when modal opens/closes or RFQ changes
  useEffect(() => {
    if (isOpen && rfq) {
      // Auto-populate subject
      setSubject(`RFQ ${rfq.refNumber}: ${rfq.title}`);
      
      // Auto-populate message template
      const defaultMessage = `Dear Vendor,

We are pleased to invite you to submit a quotation for the following requirements:

RFQ Number: ${rfq.refNumber}
Title: ${rfq.title}
Response Deadline: ${rfq.responseDeadline ? new Date(rfq.responseDeadline).toLocaleDateString() : 'Please respond at your earliest convenience'}

${rfq.description ? `Description:\n${rfq.description}\n\n` : ''}Please review the attached RFQ document for detailed specifications and requirements.

We look forward to receiving your competitive quotation.

Best regards,
${rfq.client?.contactName || 'Procurement Team'}
${rfq.client?.company || ''}`;

      setMessage(defaultMessage);
      
      // Auto-populate recipients from invited vendors
      const invitedVendorEmails = vendors
        .filter(v => rfq.invitedVendorIds?.includes(v.id))
        .map(v => v.email)
        .filter(Boolean) as string[];
      
      setRecipients(invitedVendorEmails);
      setIncludeAttachments(true);
      setError(null);
      setSuccess(false);
    } else {
      // Reset form
      setRecipients([]);
      setNewRecipient('');
      setSubject('');
      setMessage('');
      setIncludeAttachments(true);
      setShowPreview(false);
      setError(null);
      setSuccess(false);
    }
  }, [isOpen, rfq, vendors]);

  const addRecipient = () => {
    const email = newRecipient.trim();
    if (email && isValidEmail(email) && !recipients.includes(email)) {
      setRecipients([...recipients, email]);
      setNewRecipient('');
    }
  };

  const removeRecipient = (email: string) => {
    setRecipients(recipients.filter(r => r !== email));
  };

  const addVendorEmail = (vendor: Vendor) => {
    if (vendor.email && !recipients.includes(vendor.email)) {
      setRecipients([...recipients, vendor.email]);
    }
  };

  const isValidEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  const handleSend = async () => {
    if (!rfq) return;
    
    setError(null);
    
    // Validation
    if (recipients.length === 0) {
      setError('Please add at least one recipient');
      return;
    }
    
    if (!subject.trim()) {
      setError('Please enter a subject');
      return;
    }
    
    if (!message.trim()) {
      setError('Please enter a message');
      return;
    }

    setIsLoading(true);
    
    try {
      const emailData: EmailData = {
        rfqId: rfq.id,
        recipients,
        subject: subject.trim(),
        message: message.trim(),
        includeAttachments
      };
      
      await onSendEmail(emailData);
      setSuccess(true);
      
      // Close modal after a brief success display
      setTimeout(() => {
        onClose();
      }, 2000);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send email');
    } finally {
      setIsLoading(false);
    }
  };

  const availableVendors = vendors.filter(v => 
    v.email && !recipients.includes(v.email)
  );

  if (!rfq) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Send RFQ via Email
          </DialogTitle>
          <DialogDescription>
            Send {rfq.refNumber} - {rfq.title} to selected vendors
          </DialogDescription>
        </DialogHeader>

        {success ? (
          <div className="flex flex-col items-center justify-center py-8 space-y-4">
            <CheckCircle className="h-12 w-12 text-green-500" />
            <div className="text-center">
              <h3 className="text-lg font-semibold">Email Sent Successfully!</h3>
              <p className="text-muted-foreground">
                RFQ has been sent to {recipients.length} recipient{recipients.length !== 1 ? 's' : ''}
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <ScrollArea className="max-h-[60vh]">
              <div className="space-y-6 pr-4">
                {/* Recipients Section */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium">Recipients</Label>
                  
                  {/* Current Recipients */}
                  {recipients.length > 0 && (
                    <div className="flex flex-wrap gap-2 p-3 border rounded-lg bg-muted/50">
                      {recipients.map((email) => (
                        <Badge key={email} variant="secondary" className="flex items-center gap-1">
                          {email}
                          <button
                            onClick={() => removeRecipient(email)}
                            className="ml-1 hover:bg-destructive/20 rounded-full p-0.5"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                  )}
                  
                  {/* Add New Recipient */}
                  <div className="flex gap-2">
                    <Input
                      placeholder="Enter email address"
                      value={newRecipient}
                      onChange={(e) => setNewRecipient(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && addRecipient()}
                    />
                    <Button 
                      onClick={addRecipient}
                      disabled={!newRecipient.trim() || !isValidEmail(newRecipient.trim())}
                      size="sm"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  {/* Available Vendors */}
                  {availableVendors.length > 0 && (
                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground flex items-center gap-1">
                        <Users className="h-3 w-3" />
                        Available Vendors
                      </Label>
                      <div className="flex flex-wrap gap-2">
                        {availableVendors.slice(0, 5).map((vendor) => (
                          <Button
                            key={vendor.id}
                            variant="outline"
                            size="sm"
                            onClick={() => addVendorEmail(vendor)}
                            className="text-xs"
                          >
                            <Plus className="h-3 w-3 mr-1" />
                            {vendor.name} ({vendor.email})
                          </Button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                <Separator />

                {/* Subject */}
                <div className="space-y-2">
                  <Label htmlFor="subject">Subject</Label>
                  <Input
                    id="subject"
                    value={subject}
                    onChange={(e) => setSubject(e.target.value)}
                    placeholder="Email subject"
                  />
                </div>

                {/* Message */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="message">Message</Label>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowPreview(!showPreview)}
                    >
                      {showPreview ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      {showPreview ? 'Edit' : 'Preview'}
                    </Button>
                  </div>
                  
                  {showPreview ? (
                    <div className="border rounded-lg p-4 bg-muted/50 min-h-[200px]">
                      <div className="whitespace-pre-wrap text-sm">{message}</div>
                    </div>
                  ) : (
                    <Textarea
                      id="message"
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      placeholder="Email message"
                      rows={8}
                      className="resize-none"
                    />
                  )}
                </div>

                {/* Options */}
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="attachments"
                      checked={includeAttachments}
                      onChange={(e) => setIncludeAttachments(e.target.checked)}
                      className="rounded"
                    />
                    <Label htmlFor="attachments" className="text-sm">
                      Include RFQ document and attachments
                    </Label>
                  </div>
                </div>
              </div>
            </ScrollArea>
          </div>
        )}

        {!success && (
          <DialogFooter>
            <Button variant="outline" onClick={onClose} disabled={isLoading}>
              Cancel
            </Button>
            <Button onClick={handleSend} disabled={isLoading || recipients.length === 0}>
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Send Email ({recipients.length})
                </>
              )}
            </Button>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
}
