import dotenv from "dotenv";
dotenv.config();

// LangGraph-based Procurement Agent with Perplexity Sonar Pro
interface PerplexityResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
}

class PerplexityAgent {
  private apiKey: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async query(prompt: string, instructions: string): Promise<string> {
    try {
      const response = await fetch('https://api.perplexity.ai/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'sonar-pro',
          messages: [
            { role: 'system', content: instructions },
            { role: 'user', content: prompt }
          ],
          temperature: 0.7,
          max_tokens: 2000,
        }),
      });

      if (!response.ok) {
        throw new Error(`Perplexity API error: ${response.statusText}`);
      }

      const data: PerplexityResponse = await response.json();
      return data.choices[0]?.message?.content || 'No response generated';
    } catch (error) {
      console.error('Perplexity API error:', error);
      return `Error: ${error.message}`;
    }
  }
}

// LangGraph-style workflow state
interface WorkflowState {
  input: string;
  queryAnalysis?: string;
  marketResearch?: string;
  recommendations?: string;
  documents?: string;
  actionPlan?: string;
  errors: string[];
}

async function procurementAgentWorkflow({ input, env }): Promise<any> {
  const perplexityAgent = new PerplexityAgent(process.env.PERPLEXITY_API_KEY!);

  // Initialize workflow state
  const state: WorkflowState = {
    input,
    errors: []
  };

  try {
    // Step 1: Analyze user query and determine procurement intent
    console.log('Step 1: Analyzing query...');
    state.queryAnalysis = await perplexityAgent.query(
      input,
      `You are a Procurement AI Agent. Analyze the user query and determine:
      1. Type of procurement request (supplier search, RFQ generation, invoice processing, etc.)
      2. Key requirements and constraints
      3. Budget considerations
      4. Regional preferences (especially for Mangaluru, India)
      5. Compliance requirements

      Respond with a structured analysis including the procurement type and key parameters.`
    );

    // Step 2: Retrieve relevant procurement policies and supplier data
    console.log('Step 2: Retrieving procurement data...');
    // For now, use fallback data - can be enhanced with actual memory retrieval later
    const relevantMemories = [{
      text: "Fallback procurement policies: Standard approval workflows, ESG compliance required, regional supplier preferences for Mangaluru market."
    }];

    // Step 3: Search for real-time market data and supplier information
    console.log('Step 3: Conducting market research...');
    state.marketResearch = await perplexityAgent.query(
      `Research market data for: ${input}`,
      `Search for current market information related to the procurement request. Include:
      1. Current market prices and trends
      2. Available suppliers (especially in Mangaluru region if relevant)
      3. Compliance and certification requirements
      4. Sustainability and ESG factors
      5. Recent industry developments

      Provide comprehensive market intelligence for informed procurement decisions.`
    );

    // Step 4: Generate procurement recommendations
    console.log('Step 4: Generating recommendations...');
    const context = `
    Query Analysis: ${state.queryAnalysis}

    Internal Knowledge: ${relevantMemories.map(m => m.text).join('\n')}

    Market Research: ${state.marketResearch}
    `;

    state.recommendations = await perplexityAgent.query(
      `Generate procurement recommendations based on this context: ${context}`,
      `You are an expert Procurement AI Agent. Based on the analysis and research, provide:

      1. **Supplier Recommendations**: Top 3-5 suppliers with rationale
      2. **Cost Analysis**: Budget estimates and cost-saving opportunities
      3. **Risk Assessment**: Potential risks and mitigation strategies
      4. **Compliance Check**: Regulatory and ESG compliance status
      5. **Timeline**: Recommended procurement timeline
      6. **Next Steps**: Specific actions to take (RFQ, RFP, direct purchase, etc.)
      7. **Regional Considerations**: Special considerations for Mangaluru/India market
      8. **Approval Workflow**: Required approvals based on budget and policies

      Format the response as a comprehensive procurement recommendation report.`
    );

    // Step 5: Generate actionable documents (RFQ, RFP, or PO draft)
    console.log('Step 5: Generating documents...');
    state.documents = await perplexityAgent.query(
      `Generate procurement documents for: ${input}\n\nBased on recommendations: ${state.recommendations}`,
      `Based on the procurement recommendations, generate appropriate procurement documents:

      1. If supplier search: Create RFQ (Request for Quotation) template
      2. If complex procurement: Create RFP (Request for Proposal) outline
      3. If direct purchase: Create PO (Purchase Order) draft
      4. Include all necessary terms, conditions, and specifications
      5. Ensure compliance with company policies and regional regulations
      6. Add ESG and sustainability requirements where applicable

      Format as ready-to-use procurement documents.`
    );

    // Step 6: Create final procurement action plan
    console.log('Step 6: Creating action plan...');
    state.actionPlan = await perplexityAgent.query(
      `Create action plan for procurement request: ${input}

      Recommendations: ${state.recommendations}
      Documents: ${state.documents}`,
      `Create a comprehensive procurement action plan that includes:

      1. **Executive Summary**: Key findings and recommendations
      2. **Immediate Actions**: What to do next (within 24-48 hours)
      3. **Short-term Actions**: Steps for the next 1-2 weeks
      4. **Long-term Strategy**: Ongoing procurement optimization
      5. **Budget Impact**: Financial implications and savings opportunities
      6. **Risk Mitigation**: How to address identified risks
      7. **Compliance Checklist**: Ensure all regulatory requirements are met
      8. **Performance Metrics**: KPIs to track procurement success
      9. **Stakeholder Communication**: Who needs to be informed/involved
      10. **Regional Adaptations**: Specific considerations for local market

      Make it actionable and specific to the user's procurement needs.`
    );

    // Compile final response
    const finalResponse = {
      status: "success",
      query_analysis: state.queryAnalysis,
      market_intelligence: state.marketResearch,
      recommendations: state.recommendations,
      documents: state.documents,
      action_plan: state.actionPlan,
      timestamp: new Date().toISOString(),
      region: "Mangaluru, India (with global considerations)"
    };

    return finalResponse;

  } catch (err) {
    console.error("Procurement workflow error:", err);
    state.errors.push(err.message);

    // Return a fallback response instead of throwing
    return {
      status: "error",
      message: "Procurement analysis completed with limited data",
      fallback_response: `I've analyzed your procurement request: "${input}".

      Based on general procurement best practices:
      1. Verify supplier credentials and ESG compliance
      2. Compare at least 3 quotes for purchases over $5,000
      3. Consider local Mangaluru suppliers for regional advantages
      4. Ensure all documentation meets compliance requirements
      5. Follow standard approval workflows

      Please check your API keys and Perplexity setup for full functionality.`,
      errors: state.errors,
      timestamp: new Date().toISOString()
    };
  }
}

async function main(event, env) {
  try {
    const { input } = await event.json();
    const result = await procurementAgentWorkflow({ input, env });
    return result;
  } catch (error) {
    return {
      status: "error",
      message: "Failed to process procurement request",
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

export default main;

// Test function for development
export async function testProcurementAgent(input: string) {
  return await procurementAgentWorkflow({ input, env: {} });
}

// Example usage for testing
if (import.meta.url === `file://${process.argv[1]}`) {
  (async () => {
    const testInput = 'Find sustainable packaging suppliers in Mangaluru under $5,000 budget with ESG compliance';
    console.log('Testing Procurement Agent with LangGraph + Perplexity...');
    const result = await testProcurementAgent(testInput);
    console.log(JSON.stringify(result, null, 2));
  })();
}